<template>
  <el-dialog
    :modelValue="dialogVisible"
    :title="title"
    :close-on-click-modal="true"
    width="70%"
    destroy-on-close
    custom-class="metric-history-dialog"
    @update:modelValue="updateDialogVisible"
  >
    <div v-loading="loading" class="metric-history-container">
      <div class="date-picker-container">
        <div class="filter-panel">
          <div class="filter-item">
            <span class="filter-label"
              ><i class="el-icon-time" style="margin-right: 4px"></i
              >时间范围</span
            >
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :shortcuts="dateShortcuts"
              :disabled-date="disabledDate"
              value-format="YYYY-MM-DD"
              class="date-range"
              size="small"
              @change="fetchMetricHistory"
            />
          </div>
        </div>
      </div>

      <div v-if="error" class="error-message">
        <el-alert
          type="error"
          :title="error"
          show-icon
          :closable="false"
          effect="dark"
        />
      </div>
      <div v-else-if="metrics.length === 0 && !loading" class="no-data">
        <el-empty description="暂无历史数据" />
      </div>
      <div v-else-if="metrics.length > 0" class="metric-data">
        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-title">{{ title }}趋势图</div>
            <div class="chart-legend">
              <div class="legend-item">
                <div class="color-dot low"></div>
                <div class="legend-text">最小值</div>
              </div>
              <div class="legend-item">
                <div class="color-dot medium"></div>
                <div class="legend-text">指标值</div>
              </div>
              <div class="legend-item">
                <div class="color-dot high"></div>
                <div class="legend-text">最大值</div>
              </div>
            </div>
          </div>
          <div ref="chartRef" class="chart"></div>
          <div v-if="metrics.length > 0" class="chart-stats">
            <div class="stat-item">
              <div class="stat-label">
                <i class="el-icon-refresh-right"></i>
                最新值
              </div>
              <div
                class="stat-value"
                :class="{
                  positive:
                    metrics[metrics.length - 1].value >
                    metrics.reduce((sum, item) => sum + item.value, 0) /
                      metrics.length,
                  negative:
                    metrics[metrics.length - 1].value <
                    metrics.reduce((sum, item) => sum + item.value, 0) /
                      metrics.length
                }"
              >
                {{ formatValue(metrics[metrics.length - 1].value) }}
                <span v-if="metrics.length > 1" class="trend-indicator">
                  {{
                    metrics[metrics.length - 1].value >
                    metrics[metrics.length - 2].value
                      ? "↗"
                      : metrics[metrics.length - 1].value <
                          metrics[metrics.length - 2].value
                        ? "↘"
                        : "→"
                  }}
                </span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">
                <i class="el-icon-data-line"></i>
                平均值
              </div>
              <div class="stat-value neutral">
                {{
                  formatValue(
                    metrics.reduce((sum, item) => sum + item.value, 0) /
                      metrics.length
                  )
                }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">
                <i class="el-icon-top"></i>
                最大值
              </div>
              <div class="stat-value positive">
                {{ formatValue(Math.max(...metrics.map(item => item.value))) }}
                <span class="change-badge max">峰值</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">
                <i class="el-icon-bottom"></i>
                最小值
              </div>
              <div class="stat-value negative">
                {{ formatValue(Math.min(...metrics.map(item => item.value))) }}
                <span class="change-badge min">谷值</span>
              </div>
            </div>
            <div v-if="metrics.length > 1" class="stat-item">
              <div class="stat-label">
                <i class="el-icon-sort"></i>
                总体变化
              </div>
              <div
                class="stat-value"
                :class="{
                  positive: getTotalChangePercent() > 0,
                  negative: getTotalChangePercent() < 0,
                  neutral: getTotalChangePercent() === 0
                }"
              >
                {{ getTotalChangePercent() > 0 ? "+" : ""
                }}{{ getTotalChangePercent().toFixed(2) }}%
                <span class="trend-indicator">
                  {{
                    getTotalChangePercent() > 5
                      ? "📈"
                      : getTotalChangePercent() < -5
                        ? "📉"
                        : "📊"
                  }}
                </span>
              </div>
            </div>
            <div v-if="getVolatilityLevel()" class="stat-item">
              <div class="stat-label">
                <i class="el-icon-warning"></i>
                波动性
              </div>
              <div
                class="stat-value"
                :class="{
                  positive: getVolatilityLevel() === '高',
                  warning: getVolatilityLevel() === '中',
                  neutral: getVolatilityLevel() === '低'
                }"
              >
                {{ getVolatilityLevel() }}
                <span class="volatility-indicator">
                  {{
                    getVolatilityLevel() === "高"
                      ? "⚡"
                      : getVolatilityLevel() === "中"
                        ? "🌊"
                        : "🟢"
                  }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  watch,
  nextTick,
  defineProps,
  defineEmits,
  onBeforeUnmount,
  computed
} from "vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import { getResourceMetricsAPI } from "@/api/statistic/asset/metric";
import type {
  ResourceType,
  MetricType,
  MetricItem
} from "@/api/statistic/asset/metric";
import {
  formatStorage,
  formatThousands,
  formatBandwidth
} from "@/utils/format";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: "指标历史"
  },
  resourceType: {
    type: String,
    required: true
  },
  metricType: {
    type: String,
    required: true
  },
  resourceName: {
    type: String,
    default: ""
  },
  unit: {
    type: String,
    default: ""
  },
  isMemory: {
    type: Boolean,
    default: false
  },
  isNetwork: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits<{
  "update:visible": [value: boolean];
  close: [];
}>();

const dialogVisible = ref(props.visible);
const loading = ref(false);
const error = ref("");
const metrics = ref<MetricItem[]>([]);
const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 日期范围
const dateRange = ref<[string, string]>([
  dayjs().subtract(93, "days").format("YYYY-MM-DD"),
  dayjs().format("YYYY-MM-DD")
]);
const startDate = computed(() => dateRange.value[0]);
const endDate = computed(() => dateRange.value[1]);

// 格式化函数 - 移到根作用域，使其可在模板中访问
const formatValue = (value: number) => {
  let formattedValue = "";

  if (props.isMemory) {
    // 检查是否为 memory_mb_total，只有它使用 MB 单位
    if (props.metricType === "memory_mb_total") {
      // 对于 memory_mb_total，单位已经是 MB，直接格式化
      const storageStr = formatStorage(value * 1024 * 1024); // 转换为字节以便使用 formatStorage
      // 提取数字部分
      const numMatch = storageStr.match(/([\d\.]+)([A-Za-z]+)/);
      if (numMatch) {
        const num = parseFloat(numMatch[1]);
        const unit = numMatch[2];
        formattedValue = num.toFixed(2) + unit;
      } else {
        formattedValue = storageStr;
      }
    } else {
      // 其他内存指标已经是字节单位，直接格式化
      const storageStr = formatStorage(value);
      // 提取数字部分
      const numMatch = storageStr.match(/([\d\.]+)([A-Za-z]+)/);
      if (numMatch) {
        const num = parseFloat(numMatch[1]);
        const unit = numMatch[2];
        formattedValue = num.toFixed(2) + unit;
      } else {
        formattedValue = storageStr;
      }
    }
  } else if (props.isNetwork) {
    // 网络带宽格式化（保留单位和两位小数）
    const bwStr = formatBandwidth(value);
    // 提取数字部分
    const numMatch = bwStr.match(/([\d\.]+)([A-Za-z\/]+)/);
    if (numMatch) {
      const num = parseFloat(numMatch[1]);
      const unit = numMatch[2];
      formattedValue = num.toFixed(2) + unit;
    } else {
      formattedValue = bwStr;
    }
  } else {
    // 其他数字格式化（保留两位小数）
    const numStr = formatThousands(parseFloat(value.toFixed(2)));
    formattedValue = numStr + (props.unit ? ` ${props.unit}` : "");
  }

  return formattedValue;
};

// 计算总体变化百分比
const getTotalChangePercent = () => {
  if (metrics.value.length < 2) return 0;

  const firstValue = metrics.value[0].value;
  const lastValue = metrics.value[metrics.value.length - 1].value;

  if (firstValue === 0) return lastValue > 0 ? 100 : 0;

  return ((lastValue - firstValue) / firstValue) * 100;
};

// 计算波动性水平
const getVolatilityLevel = () => {
  if (metrics.value.length < 3) return null;

  const values = metrics.value.map(item => item.value);
  const avgValue = values.reduce((sum, val) => sum + val, 0) / values.length;

  // 计算变异系数 (CV = 标准差 / 平均值)
  const variance =
    values.reduce((sum, val) => sum + Math.pow(val - avgValue, 2), 0) /
    values.length;
  const stdDev = Math.sqrt(variance);
  const cv = avgValue !== 0 ? (stdDev / avgValue) * 100 : 0;

  // 根据变异系数判断波动性
  if (cv > 30) return "高";
  if (cv > 15) return "中";
  if (cv > 5) return "低";
  return null; // 变化很小，不显示波动性
};

// 禁用未来日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  },
  {
    text: "最近半年",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180); // 半年大约180天
      return [start, end];
    }
  },
  {
    text: "最近一年",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365); // 一年365天
      return [start, end];
    }
  }
];

// 监听visible属性变化
watch(
  () => props.visible,
  newVal => {
    dialogVisible.value = newVal;
    if (newVal) {
      fetchMetricHistory();
    }
  }
);

// 更新对话框可见状态
const updateDialogVisible = (val: boolean) => {
  dialogVisible.value = val;
  if (!val) {
    emit("update:visible", false);
    emit("close");
  }
};

// 获取指标历史数据
const fetchMetricHistory = async () => {
  loading.value = true;
  error.value = "";
  metrics.value = [];

  try {
    const response = await getResourceMetricsAPI(
      props.resourceType as ResourceType,
      props.metricType as MetricType,
      props.resourceName,
      startDate.value,
      endDate.value
    );

    if (response && response.success) {
      // 获取数据成功
      let metricsData = [];

      if (Array.isArray(response.data)) {
        // 如果直接返回数组
        metricsData = response.data;
      } else if (response.data && response.data.metrics) {
        // 如果返回对象中包含metrics数组
        metricsData = response.data.metrics;
      }

      // 设置图表数据
      if (metricsData && metricsData.length > 0) {
        metrics.value = metricsData;
        nextTick(() => {
          initChart();
        });
      } else {
        // 没有数据
        error.value = "暂无历史数据";
      }
    } else {
      // 失败时显示错误信息
      error.value = response.msg || "获取历史数据失败";
    }
  } catch (err) {
    error.value = "获取历史数据失败，请稍后重试";
  } finally {
    loading.value = false;
  }
};

// 计算数据统计信息
const calculateDataStats = () => {
  if (!metrics.value.length) return null;

  const values = metrics.value.map(item => item.value);
  const avgValue = values.reduce((sum, val) => sum + val, 0) / values.length;
  const maxValue = Math.max(...values);
  const minValue = Math.min(...values);
  const latestValue = values[values.length - 1];
  const firstValue = values[0];

  // 计算总体变化率
  const totalChangePercent =
    firstValue !== 0 ? ((latestValue - firstValue) / firstValue) * 100 : 0;

  // 计算标准差，用于识别异常值
  const variance =
    values.reduce((sum, val) => sum + Math.pow(val - avgValue, 2), 0) /
    values.length;
  const stdDev = Math.sqrt(variance);

  return {
    avgValue,
    maxValue,
    minValue,
    latestValue,
    firstValue,
    totalChangePercent,
    stdDev,
    values
  };
};

// 识别异常值和显著变化点
const identifyAnomalies = (stats: any) => {
  if (!stats) return [];

  const { avgValue, stdDev, values } = stats;
  const anomalies: any[] = [];

  values.forEach((value: number, index: number) => {
    // 使用2倍标准差作为异常值阈值
    const threshold = 2 * stdDev;
    if (Math.abs(value - avgValue) > threshold) {
      anomalies.push({
        coord: [index, value],
        name: value > avgValue ? "异常高值" : "异常低值",
        itemStyle: {
          color: value > avgValue ? "#FF6B6B" : "#4ECDC4"
        }
      });
    }
  });

  return anomalies;
};

// 初始化图表
const initChart = () => {
  // 确保Chart DOM已经加载
  if (!chartRef.value) return;

  // 销毁旧图表
  if (chart) {
    chart.dispose();
    chart = null;
  }

  // 创建新图表
  chart = echarts.init(chartRef.value, null, { renderer: "canvas" });

  // 准备数据
  const dates = metrics.value.map(item => item.date);
  const values = metrics.value.map(item => item.value);
  const stats = calculateDataStats();
  const anomalies = identifyAnomalies(stats);

  // 创建渐变色
  const gradientColor = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
    { offset: 0, color: "#667eea" },
    { offset: 0.5, color: "#764ba2" },
    { offset: 1, color: "#f093fb" }
  ]);

  // 图表配置
  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
          borderRadius: 4
        },
        lineStyle: {
          color: "#667eea",
          type: "dashed",
          width: 2
        },
        crossStyle: {
          color: "#667eea",
          width: 1,
          opacity: 0.6
        }
      },
      backgroundColor: "rgba(255, 255, 255, 0.98)",
      borderWidth: 1,
      borderColor: "#e4e7ed",
      borderRadius: 8,
      padding: 16,
      textStyle: {
        color: "#303133",
        fontSize: 13
      },
      extraCssText:
        "box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12); backdrop-filter: blur(8px);",
      formatter: function (params: any) {
        if (!params || !params.length || !metrics.value.length) return "";

        const date = params[0].axisValue;
        const value = params[0].value;
        const dataIndex = params[0].dataIndex;

        if (!stats) return "";

        const { avgValue } = stats;

        // 计算与平均值的偏差百分比
        const diffPercent =
          avgValue !== 0 ? ((value - avgValue) / avgValue) * 100 : 0;
        const diffSymbol = diffPercent >= 0 ? "+" : "";
        const diffColor =
          diffPercent > 10
            ? "#f56c6c"
            : diffPercent < -10
              ? "#67c23a"
              : "#909399";

        // 计算相对于前一天的变化
        let dayChangePercent = 0;
        let dayChangeSymbol = "";
        let dayChangeColor = "#909399";

        if (dataIndex > 0) {
          const prevValue = values[dataIndex - 1];
          if (prevValue !== 0) {
            dayChangePercent = ((value - prevValue) / prevValue) * 100;
            dayChangeSymbol = dayChangePercent >= 0 ? "+" : "";
            dayChangeColor =
              dayChangePercent > 5
                ? "#f56c6c"
                : dayChangePercent < -5
                  ? "#67c23a"
                  : "#909399";
          }
        }

        // 判断是否为异常值
        const isAnomaly = anomalies.some(
          anomaly => anomaly.coord[0] === dataIndex
        );
        const anomalyBadge = isAnomaly
          ? `<span style="background: linear-gradient(45deg, #ff6b6b, #ffa500); color: white; padding: 2px 6px; border-radius: 10px; font-size: 10px; margin-left: 8px;">异常</span>`
          : "";

        return `
          <div style="padding: 4px 0; font-weight: 600; border-bottom: 1px solid #eee; margin-bottom: 8px; display: flex; align-items: center;">
            <i class="el-icon-time" style="margin-right: 6px; color: #667eea;"></i>
            ${date}
            ${anomalyBadge}
          </div>
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="display:inline-block;margin-right:8px;border-radius:50%;width:10px;height:10px;background: linear-gradient(45deg, #667eea, #764ba2);"></span>
            <span style="flex: 1; font-weight: 500;">${props.title}</span>
            <span style="font-weight: 700; margin-left: 16px; color: #303133; font-size: 14px;">${formatValue(value)}</span>
          </div>
          ${
            dataIndex > 0
              ? `
          <div style="font-size: 12px; color: #606266; margin-bottom: 6px; display: flex; align-items: center;">
            <i class="el-icon-sort" style="margin-right: 4px;"></i>
            <span>日变化: </span>
            <span style="color: ${dayChangeColor}; font-weight: 600; margin-left: 4px;">
              ${dayChangeSymbol}${Math.abs(dayChangePercent).toFixed(2)}%
              ${dayChangePercent > 0 ? "↗" : dayChangePercent < 0 ? "↘" : "→"}
            </span>
          </div>`
              : ""
          }
          <div style="font-size: 12px; color: #909399; display: flex; align-items: center;">
            <i class="el-icon-data-line" style="margin-right: 4px;"></i>
            <span>与平均值偏差: </span>
            <span style="color: ${diffColor}; font-weight: 600; margin-left: 4px;">
              ${diffSymbol}${Math.abs(diffPercent).toFixed(2)}%
              ${Math.abs(diffPercent) > 10 ? (diffPercent > 0 ? "📈" : "📉") : "📊"}
            </span>
          </div>
          ${
            stats.totalChangePercent !== 0
              ? `
          <div style="font-size: 11px; color: #c0c4cc; margin-top: 6px; padding-top: 6px; border-top: 1px dashed #eee;">
            总体趋势: <span style="color: ${stats.totalChangePercent > 0 ? "#67c23a" : "#f56c6c"}; font-weight: 500;">
              ${stats.totalChangePercent > 0 ? "+" : ""}${stats.totalChangePercent.toFixed(2)}%
            </span>
          </div>`
              : ""
          }
        `;
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "18%",
      top: "12%",
      containLabel: true
    },
    toolbox: {
      feature: {
        dataZoom: {
          yAxisIndex: "none",
          title: {
            zoom: "区域缩放",
            back: "还原"
          },
          iconStyle: {
            borderColor: "#667eea"
          },
          emphasis: {
            iconStyle: {
              borderColor: "#409EFF"
            }
          }
        },
        saveAsImage: {
          title: "保存图片",
          iconStyle: {
            borderColor: "#667eea"
          },
          emphasis: {
            iconStyle: {
              borderColor: "#409EFF"
            }
          }
        },
        restore: {
          title: "重置",
          iconStyle: {
            borderColor: "#667eea"
          },
          emphasis: {
            iconStyle: {
              borderColor: "#409EFF"
            }
          }
        },
        magicType: {
          type: ["line", "bar"],
          title: {
            line: "切换为折线图",
            bar: "切换为柱状图"
          },
          iconStyle: {
            borderColor: "#667eea"
          },
          emphasis: {
            iconStyle: {
              borderColor: "#409EFF"
            }
          }
        }
      },
      right: 20,
      top: 8,
      itemSize: 16,
      itemGap: 10,
      iconStyle: {
        borderColor: "#667eea",
        borderWidth: 1
      },
      emphasis: {
        iconStyle: {
          borderColor: "#409EFF",
          borderWidth: 2
        }
      }
    },
    dataZoom: [
      {
        type: "slider",
        show: true,
        start: 0,
        end: 100,
        bottom: 8,
        height: 24,
        borderColor: "transparent",
        backgroundColor: "#f8f9fa",
        fillerColor: "rgba(102, 126, 234, 0.2)",
        handleStyle: {
          color: "#667eea",
          borderColor: "#667eea",
          borderWidth: 2,
          shadowBlur: 4,
          shadowColor: "rgba(102, 126, 234, 0.3)"
        },
        textStyle: {
          color: "#606266",
          fontSize: 11
        },
        brushSelect: false,
        selectedDataBackground: {
          lineStyle: {
            color: "#667eea",
            opacity: 0.8
          },
          areaStyle: {
            color: "rgba(102, 126, 234, 0.1)"
          }
        },
        dataBackground: {
          lineStyle: {
            color: "#c0c4cc",
            opacity: 0.5
          },
          areaStyle: {
            color: "rgba(192, 196, 204, 0.1)"
          }
        }
      },
      {
        type: "inside",
        xAxisIndex: 0,
        filterMode: "none"
      }
    ],
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: dates,
      axisLine: {
        lineStyle: {
          color: "#e4e7ed",
          width: 1
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: "#e4e7ed"
        },
        length: 4
      },
      axisLabel: {
        color: "#606266",
        fontSize: 12,
        fontWeight: 500,
        margin: 12,
        formatter: function (value: string) {
          // 优化日期显示格式
          if (dates.length > 30) {
            // 数据点较多时，只显示月-日
            return dayjs(value).format("MM-DD");
          } else if (dates.length > 7) {
            // 中等数据量时，显示月/日
            return dayjs(value).format("M/D");
          } else {
            // 数据点较少时，显示完整日期
            return dayjs(value).format("MM-DD");
          }
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "#f5f7fa",
          width: 1
        }
      }
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: function (value: number) {
          return formatValue(value);
        },
        color: "#606266",
        fontSize: 12,
        fontWeight: 500,
        margin: 16
      },
      splitLine: {
        lineStyle: {
          type: "dashed",
          color: "#f0f2f5",
          width: 1
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      // 优化Y轴刻度，确保变化清晰可见
      min: function (value: any) {
        if (!stats || !stats.values.length) return value.min;

        const { minValue, maxValue, avgValue } = stats;
        const range = maxValue - minValue;

        // 如果数据变化很小，扩大显示范围以突出变化
        if (range < avgValue * 0.1) {
          return Math.max(0, minValue - avgValue * 0.05);
        }

        // 正常情况下，留出一些边距
        return Math.max(0, minValue - range * 0.1);
      },
      max: function (value: any) {
        if (!stats || !stats.values.length) return value.max;

        const { minValue, maxValue, avgValue } = stats;
        const range = maxValue - minValue;

        // 如果数据变化很小，扩大显示范围以突出变化
        if (range < avgValue * 0.1) {
          return maxValue + avgValue * 0.05;
        }

        // 正常情况下，留出一些边距
        return maxValue + range * 0.1;
      }
    },
    series: [
      {
        name: props.title,
        type: "line",
        data: values,
        smooth: 0.3, // 适度平滑，保持数据真实性
        symbolSize: function (value: number, _params: any) {
          // 根据数据重要性调整点的大小
          if (!stats) return 8;

          const { avgValue, stdDev } = stats;
          const deviation = Math.abs(value - avgValue);

          // 异常值使用更大的点
          if (deviation > 2 * stdDev) return 12;
          if (deviation > stdDev) return 10;
          return 8;
        },
        showSymbol: true, // 始终显示数据点
        symbol: function (value: number, _params: any) {
          if (!stats) return "circle";

          const { avgValue, stdDev } = stats;
          const deviation = Math.abs(value - avgValue);

          // 异常值使用特殊符号
          if (deviation > 2 * stdDev) {
            return value > avgValue ? "triangle" : "diamond";
          }

          return "circle";
        },
        emphasis: {
          focus: "series",
          scale: 1.5,
          itemStyle: {
            shadowBlur: 15,
            shadowColor: "rgba(102, 126, 234, 0.6)",
            borderWidth: 3,
            borderColor: "#fff"
          },
          lineStyle: {
            width: 5,
            shadowBlur: 15,
            shadowColor: "rgba(102, 126, 234, 0.4)"
          }
        },
        lineStyle: {
          width: 4,
          join: "round",
          cap: "round",
          color: gradientColor,
          shadowColor: "rgba(102, 126, 234, 0.3)",
          shadowBlur: 8,
          shadowOffsetY: 3
        },
        itemStyle: {
          borderWidth: 3,
          borderColor: "#fff",
          color: function (params: any) {
            if (!stats) return "#667eea";

            const value = params.value;
            const { avgValue, stdDev } = stats;
            const deviation = Math.abs(value - avgValue);

            // 异常值使用特殊颜色
            if (deviation > 2 * stdDev) {
              return value > avgValue ? "#FF6B6B" : "#4ECDC4";
            }

            // 根据与平均值的偏差程度设置颜色
            if (value > avgValue + stdDev) return "#F093FB";
            if (value < avgValue - stdDev) return "#667EEA";

            return "#764BA2";
          },
          shadowBlur: 6,
          shadowColor: "rgba(102, 126, 234, 0.3)"
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(102, 126, 234, 0.25)"
            },
            {
              offset: 0.5,
              color: "rgba(118, 75, 162, 0.15)"
            },
            {
              offset: 1,
              color: "rgba(240, 147, 251, 0.05)"
            }
          ]),
          shadowColor: "rgba(102, 126, 234, 0.1)",
          shadowBlur: 10
        },
        markPoint: {
          symbol: "pin",
          symbolSize: function (value: number) {
            if (!stats) return 40;

            const { avgValue, stdDev } = stats;
            const deviation = Math.abs(value - avgValue);

            // 异常值使用更大的标记
            if (deviation > 2 * stdDev) return 50;
            return 40;
          },
          data: [
            {
              type: "max",
              name: "最大值",
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#FF6B6B" },
                  { offset: 1, color: "#FF8E8E" }
                ])
              },
              label: {
                formatter: (params: any) =>
                  `最大\n${formatValue(params.value)}`,
                color: "#fff",
                fontSize: 11,
                fontWeight: "bold",
                lineHeight: 14
              }
            },
            {
              type: "min",
              name: "最小值",
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#4ECDC4" },
                  { offset: 1, color: "#7EDDD8" }
                ])
              },
              label: {
                formatter: (params: any) =>
                  `最小\n${formatValue(params.value)}`,
                color: "#fff",
                fontSize: 11,
                fontWeight: "bold",
                lineHeight: 14
              }
            },
            // 添加异常值标记
            ...anomalies.map(anomaly => ({
              ...anomaly,
              symbol: "circle",
              symbolSize: 25,
              label: {
                formatter: "⚠",
                color: "#fff",
                fontSize: 14,
                fontWeight: "bold"
              }
            }))
          ],
          emphasis: {
            scale: 1.3,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: "rgba(0, 0, 0, 0.3)"
            }
          }
        },
        markLine: {
          silent: false,
          symbol: ["none", "arrow"],
          symbolSize: [0, 8],
          lineStyle: {
            type: "dashed",
            width: 2,
            color: "#909399",
            opacity: 0.8
          },
          data: [
            {
              type: "average",
              name: "平均值",
              lineStyle: {
                color: "#667eea",
                width: 2,
                type: "dashed"
              },
              label: {
                formatter: (params: any) =>
                  `平均值: ${formatValue(params.value)}`,
                position: "end",
                show: true,
                color: "#667eea",
                fontSize: 12,
                fontWeight: 600,
                backgroundColor: "rgba(255,255,255,0.95)",
                padding: [6, 8],
                borderRadius: 4,
                borderColor: "#667eea",
                borderWidth: 1,
                shadowBlur: 4,
                shadowColor: "rgba(102, 126, 234, 0.2)"
              }
            },
            // 添加趋势线（如果有明显趋势）
            ...(stats && Math.abs(stats.totalChangePercent) > 10
              ? [
                  {
                    name: "趋势线",
                    type: "linear",
                    lineStyle: {
                      color:
                        stats.totalChangePercent > 0 ? "#67c23a" : "#f56c6c",
                      width: 2,
                      type: "solid",
                      opacity: 0.6
                    },
                    label: {
                      formatter: `趋势: ${stats.totalChangePercent > 0 ? "+" : ""}${stats.totalChangePercent.toFixed(1)}%`,
                      position: "middle",
                      color:
                        stats.totalChangePercent > 0 ? "#67c23a" : "#f56c6c",
                      fontSize: 11,
                      fontWeight: 600,
                      backgroundColor: "rgba(255,255,255,0.9)",
                      padding: [4, 6],
                      borderRadius: 3
                    }
                  }
                ]
              : [])
          ],
          emphasis: {
            lineStyle: {
              width: 3,
              opacity: 1
            }
          }
        },
        animation: true,
        animationDuration: 2000,
        animationEasing: "elasticOut",
        animationDelay: function (idx: number) {
          return idx * 50; // 数据点依次出现的动画效果
        }
      }
    ]
  };

  chart.setOption(option);

  // 监听窗口大小变化，自适应调整
  const resizeHandler = () => {
    chart && chart.resize();
  };
  window.addEventListener("resize", resizeHandler);

  // 销毁时移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener("resize", resizeHandler);
    chart && chart.dispose();
  });

  return chart;
};

// 初始加载
onMounted(() => {
  if (props.visible) {
    fetchMetricHistory();
  }
});
</script>

<style scoped>
/* Enhanced responsive design */
@media screen and (width <= 1200px) {
  .chart-stats {
    gap: 12px;
    padding: 16px;
  }

  .stat-item {
    min-width: 100px;
    padding: 10px 6px;
  }

  .stat-value {
    font-size: 15px;
  }
}

@media screen and (width <= 768px) {
  .filter-panel {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .chart {
    height: 280px;
  }

  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .chart-legend {
    flex-wrap: wrap;
    gap: 8px;
  }

  .chart-stats {
    flex-wrap: wrap;
    gap: 8px;
    padding: 12px;
  }

  .stat-item {
    min-width: calc(50% - 4px);
    padding: 8px 4px;
  }

  .stat-value {
    font-size: 14px;
  }

  .stat-label {
    font-size: 11px;
  }

  .date-range {
    width: 100%;
    max-width: 280px;
  }
}

@media screen and (width <= 480px) {
  .chart {
    height: 250px;
  }

  .chart-stats {
    flex-direction: column;
    gap: 8px;
  }

  .stat-item {
    min-width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    padding: 12px;
  }

  .stat-label {
    margin-bottom: 0;
    flex: 1;
  }

  .stat-value {
    flex: none;
    font-size: 15px;
  }

  .filter-item {
    width: 100%;
  }

  .date-range {
    width: 100%;
  }
}

/* Touch-friendly enhancements for mobile */
@media (hover: none) and (pointer: coarse) {
  .stat-item {
    padding: 16px 12px;
  }

  .stat-item:hover {
    transform: none;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
  }

  .stat-item:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

/* Dialog styles */
:deep(.metric-history-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

:deep(.el-dialog__headerbtn) {
  top: 16px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

.metric-history-container {
  min-height: 400px;
}

.date-picker-container {
  margin-bottom: 20px;
}

.filter-panel {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: space-between;
}

.filter-label {
  display: flex;
  align-items: center;
  margin-right: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.error-message {
  padding: 20px 0;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.metric-data {
  margin-top: 10px;
}

.chart-container {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.chart-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  gap: 5px;
  align-items: center;
}

.legend-text {
  font-size: 12px;
  color: #909399;
}

.color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.color-dot.low {
  background-color: #67c23a;
}

.color-dot.medium {
  background-color: #409eff;
}

.color-dot.high {
  background-color: #e6a23c;
}

.filter-item {
  display: flex;
  align-items: center;
}

.chart {
  height: 350px;
  margin-bottom: 20px;
}

.chart-stats {
  display: flex;
  gap: 16px;
  justify-content: space-between;
  padding: 20px;
  margin-top: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.chart-stats::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.stat-item {
  flex: 1;
  min-width: 120px;
  text-align: center;
  position: relative;
  padding: 12px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.9);
}

.stat-label {
  margin-bottom: 8px;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.stat-label i {
  font-size: 14px;
  opacity: 0.8;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: #303133;
  line-height: 1.4;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  flex-wrap: wrap;
}

.stat-value.positive {
  color: #67c23a;
}

.stat-value.negative {
  color: #f56c6c;
}

.stat-value.neutral {
  color: #409eff;
}

.stat-value.warning {
  color: #e6a23c;
}

.trend-indicator {
  font-size: 14px;
  margin-left: 4px;
  animation: pulse 2s infinite;
}

.volatility-indicator {
  font-size: 12px;
  margin-left: 4px;
}

.change-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  color: white;
  font-weight: 500;
  margin-left: 6px;
}

.change-badge.max {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.change-badge.min {
  background: linear-gradient(45deg, #4ecdc4, #7eddd8);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.date-range {
  width: 320px;
}
</style>
