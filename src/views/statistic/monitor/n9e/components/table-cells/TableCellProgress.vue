<template>
  <el-progress
    :percentage="percentage"
    :format="formatProgress"
    :color="progressColor"
    :stroke-width="10"
    class="progress-cell"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface TableColumn {
  prop: string
  label: string
}

interface Props {
  row: any
  column: TableColumn
  themeColor: string
}

const props = defineProps<Props>()

const percentage = computed(() => {
  return props.row[props.column.prop] || 0
})

const progressColor = computed(() => {
  const rate = percentage.value
  if (rate >= 80) return '#10b981' // 绿色
  if (rate >= 60) return '#3b82f6' // 蓝色
  if (rate >= 40) return '#f97316' // 橙色
  return '#ef4444' // 红色
})

const formatProgress = (val: number) => {
  return `${val.toFixed(2)}%`
}
</script>

<style scoped>
.progress-cell {
  margin: 4px 0;
}

:deep(.el-progress-bar__outer) {
  background-color: rgb(0 0 0 / 10%);
  border-radius: 5px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 5px;
  transition: all 0.6s ease;
}

:deep(.el-progress__text) {
  font-size: 12px;
  font-weight: 600;
}
</style>
