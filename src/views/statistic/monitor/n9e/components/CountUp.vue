<template>
  <span>{{ displayValue }}</span>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";

interface Props {
  endVal: number;
  startVal?: number;
  duration?: number;
  decimals?: number;
  separator?: string;
}

const props = withDefaults(defineProps<Props>(), {
  startVal: 0,
  duration: 1000,
  decimals: 0,
  separator: ","
});

const formatNumber = (num: number): string => {
  const fixed = num.toFixed(props.decimals);
  const parts = fixed.split(".");
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, props.separator);
  return parts.join(".");
};

const displayValue = ref<string>(formatNumber(props.startVal));

const countUp = () => {
  const startTime = Date.now();
  const startVal = props.startVal;
  const endVal = props.endVal;
  const duration = props.duration;

  const animate = () => {
    const now = Date.now();
    const progress = Math.min((now - startTime) / duration, 1);

    // 使用缓动函数
    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
    const currentVal = startVal + (endVal - startVal) * easeOutQuart;

    displayValue.value = formatNumber(currentVal);

    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      displayValue.value = formatNumber(endVal);
    }
  };

  requestAnimationFrame(animate);
};

watch(
  () => props.endVal,
  () => {
    countUp();
  },
  { immediate: false }
);

onMounted(() => {
  countUp();
});
</script>
