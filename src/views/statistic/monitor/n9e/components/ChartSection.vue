<template>
  <el-card shadow="hover" class="chart-section">
    <template #header>
      <div class="flex items-center justify-between py-1">
        <div class="flex items-center">
          <el-icon class="mr-2 text-primary">
            <PieChart />
          </el-icon>
          <span class="font-bold text-gray-700">告警类型分布</span>
        </div>
        <div class="flex items-center space-x-2">
          <el-tooltip content="切换为环形图" placement="top">
            <el-button
              :type="chartType === 'doughnut' ? 'primary' : 'default'"
              size="small"
              circle
              @click="chartType = 'doughnut'"
            >
              <el-icon><PieChart /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="切换为饼图" placement="top">
            <el-button
              :type="chartType === 'pie' ? 'primary' : 'default'"
              size="small"
              circle
              @click="chartType = 'pie'"
            >
              <el-icon><PieChart /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>

    <div class="chart-container">
      <AlertChart
        v-if="hasData"
        :chart-type="chartType"
        :chart-data="chartData"
        class="w-full h-full"
      />
      <el-empty
        v-else
        description="暂无告警分布数据"
        :image-size="120"
        class="empty-state"
      >
        <template #image>
          <div class="empty-icon">
            <el-icon class="text-6xl text-gray-300">
              <PieChart />
            </el-icon>
          </div>
        </template>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { PieChart } from "@element-plus/icons-vue";
import AlertChart from "./AlertChart.vue";

interface Props {
  chartData: any;
}

const props = defineProps<Props>();

const chartType = ref<"pie" | "doughnut">("doughnut");

const hasData = computed(() => {
  return props.chartData?.pieData && props.chartData.pieData.length > 0;
});
</script>

<style scoped>
.chart-section {
  overflow: hidden;
  border-radius: 8px;
}

.chart-container {
  position: relative;
  height: 320px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.6;
}
</style>
