<template>
  <div class="n9e-statistics-container">
    <!-- 页面头部 -->
    <PageHeader title="夜莺告警统计" :icon="AlarmClock" class="mb-6">
      <template #content>
        <DateRangeSelector
          @day-change="handleDayChange"
          @range-change="handleDateRangeChange"
        />
      </template>
    </PageHeader>

    <!-- 总体告警统计 -->
    <OverviewSection
      :overview="overview"
      :loading="overviewLoading"
      :date-range="currentDateRange"
      :chart-data="overviewPieChartData"
      class="mb-6"
      @refresh="loadOverviewData"
    />
    <!-- 实例维度告警统计 -->
    <DataTableSection
      title="实例维度告警统计"
      :icon="Monitor"
      theme-color="orange"
      :date-range="currentDateRange"
      :table-data="instanceTableData"
      :columns="instanceColumns"
      :loading="instanceLoading"
      :total="instanceTableTotal"
      :current-page="instanceCurrentPage"
      :page-size="instancePageSize"
      :sort-column="instanceSortColumn"
      :sort-order="instanceSortOrder"
      empty-text="暂无实例告警数据"
      class="mb-6"
      @sort-change="handleInstanceSortChange"
      @size-change="handleInstanceSizeChange"
      @current-change="handleInstancePageChange"
      @export="handleInstanceExport"
      @refresh="loadInstanceData"
    />
    <!-- 规则维度告警统计 -->
    <DataTableSection
      title="规则维度告警统计"
      :icon="SetUp"
      theme-color="blue"
      :date-range="currentDateRange"
      :table-data="ruleTableData"
      :columns="ruleColumns"
      :loading="ruleLoading"
      :total="ruleTableTotal"
      :current-page="ruleCurrentPage"
      :page-size="rulePageSize"
      :sort-column="ruleSortColumn"
      :sort-order="ruleSortOrder"
      empty-text="暂无规则告警数据"
      class="mb-6"
      @sort-change="handleRuleSortChange"
      @size-change="handleRuleSizeChange"
      @current-change="handleRulePageChange"
      @export="handleRuleExport"
      @refresh="loadRuleData"
    />
    <!-- 集群维度告警统计 -->
    <DataTableSection
      title="集群维度告警统计"
      :icon="Grid"
      theme-color="green"
      :date-range="currentDateRange"
      :table-data="clusterTableData"
      :columns="clusterColumns"
      :loading="clusterLoading"
      :total="clusterTableTotal"
      :current-page="clusterCurrentPage"
      :page-size="clusterPageSize"
      :sort-column="clusterSortColumn"
      :sort-order="clusterSortOrder"
      empty-text="暂无集群告警数据"
      class="mb-6"
      @sort-change="handleClusterSortChange"
      @size-change="handleClusterSizeChange"
      @current-change="handleClusterPageChange"
      @export="handleClusterExport"
      @refresh="loadClusterData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { AlarmClock, Monitor, SetUp, Grid } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import DateRangeSelector from "./components/DateRangeSelector.vue";
import PageHeader from "./components/PageHeader.vue";
import OverviewSection from "./components/OverviewSection.vue";
import DataTableSection from "./components/DataTableSection.vue";
import {
  getAlertStatisticsAPI,
  getAlertStatisticsByInstanceAPI,
  getAlertStatisticsByRuleAPI,
  getAlertStatisticsByClusterAPI,
  type AlertStatisticsParams,
  type AlertStatistics,
  type InstanceAlertStatistics,
  type RuleAlertStatistics,
  type ClusterAlertStatistics
} from "@/api/statistic/monitor/n9e";

// 当前日期范围
const currentDateRange = reactive<AlertStatisticsParams>({
  start_time: "",
  end_time: ""
});

// 当前选中的日期
const selectedDay = ref<string>("");

// 总体告警统计
const overview = ref<AlertStatistics | null>(null);
const overviewLoading = ref(false);
const overviewPieChartData = computed(() => {
  if (!overview.value) return { pieData: [] };

  return {
    pieData: [
      {
        name: "紧急告警",
        value: overview.value.emergency_count,
        itemStyle: { color: "#ef4444" }
      },
      {
        name: "警告告警",
        value: overview.value.warning_count,
        itemStyle: { color: "#f97316" }
      },
      {
        name: "通知告警",
        value: overview.value.notice_count,
        itemStyle: { color: "#3b82f6" }
      }
    ].filter(item => item.value > 0) // 过滤掉数量为0的数据
  };
});

// 实例维度告警统计
const instanceTableData = ref<InstanceAlertStatistics[]>([]);
const instanceTableTotal = ref(0);
const instanceLoading = ref(false);
const instanceCurrentPage = ref(1);
const instancePageSize = ref(10);
const instanceSortColumn = ref("total_count");
const instanceSortOrder = ref("descending");

// 规则维度告警统计
const ruleTableData = ref<RuleAlertStatistics[]>([]);
const ruleTableTotal = ref(0);
const ruleLoading = ref(false);
const ruleCurrentPage = ref(1);
const rulePageSize = ref(10);
const ruleSortColumn = ref("trigger_count");
const ruleSortOrder = ref("descending");

// 集群维度告警统计
const clusterTableData = ref<ClusterAlertStatistics[]>([]);
const clusterTableTotal = ref(0);
const clusterLoading = ref(false);
const clusterCurrentPage = ref(1);
const clusterPageSize = ref(10);
const clusterSortColumn = ref("total_count");
const clusterSortOrder = ref("descending");

// 表格列配置
const instanceColumns = [
  {
    prop: "target_ident",
    label: "实例标识",
    minWidth: "180",
    sortable: "custom",
    showOverflowTooltip: true
  },
  {
    prop: "target_note",
    label: "实例备注",
    minWidth: "150",
    showOverflowTooltip: true
  },
  {
    prop: "total_count",
    label: "告警总数",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "emergency_count",
    label: "紧急告警",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "warning_count",
    label: "警告告警",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "notice_count",
    label: "通知告警",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "recovery_rate",
    label: "恢复率",
    minWidth: "100",
    sortable: "custom",
    component: "progress"
  },
  {
    prop: "avg_duration",
    label: "平均持续时间",
    minWidth: "120",
    sortable: "custom",
    component: "duration"
  }
];

const ruleColumns = [
  {
    prop: "rule_name",
    label: "规则名称",
    minWidth: "180",
    sortable: "custom",
    showOverflowTooltip: true
  },
  {
    prop: "rule_note",
    label: "规则备注",
    minWidth: "150",
    showOverflowTooltip: true
  },
  {
    prop: "trigger_count",
    label: "触发次数",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "affected_instances",
    label: "影响实例数",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "emergency_count",
    label: "紧急告警",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "warning_count",
    label: "警告告警",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "recovery_rate",
    label: "恢复率",
    minWidth: "100",
    sortable: "custom",
    component: "progress"
  },
  {
    prop: "avg_duration",
    label: "平均持续时间",
    minWidth: "120",
    sortable: "custom",
    component: "duration"
  }
];

const clusterColumns = [
  {
    prop: "cluster",
    label: "集群名称",
    minWidth: "180",
    sortable: "custom",
    showOverflowTooltip: true
  },
  {
    prop: "total_count",
    label: "告警总数",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "emergency_count",
    label: "紧急告警",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "warning_count",
    label: "警告告警",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "notice_count",
    label: "通知告警",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "affected_instances",
    label: "影响实例数",
    minWidth: "100",
    sortable: "custom"
  },
  {
    prop: "recovery_rate",
    label: "恢复率",
    minWidth: "100",
    sortable: "custom",
    component: "progress"
  },
  {
    prop: "avg_duration",
    label: "平均持续时间",
    minWidth: "120",
    sortable: "custom",
    component: "duration"
  }
];

const handleDayChange = (day: string) => {
  selectedDay.value = day;

  // 将日期设置为当天的开始时间和结束时间
  const date = new Date(day);
  const startDate = new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    0,
    0,
    0
  );
  const endDate = new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    23,
    59,
    59
  );

  // 使用formatDateTime函数格式化日期时间
  currentDateRange.start_time = formatDateTime(startDate);
  currentDateRange.end_time = formatDateTime(endDate);

  // 加载数据
  loadAllData();
};

const handleDateRangeChange = (range: {
  start_time: string;
  end_time: string;
}) => {
  currentDateRange.start_time = range.start_time;
  currentDateRange.end_time = range.end_time;

  // 重新加载所有数据
  loadAllData();
};

// 加载数据函数
const loadAllData = () => {
  loadOverviewData();
  loadInstanceData();
  loadRuleData();
  loadClusterData();
};

const loadOverviewData = async () => {
  if (!currentDateRange.start_time || !currentDateRange.end_time) {
    ElMessage.warning("请选择日期范围");
    return;
  }

  overviewLoading.value = true;
  try {
    const res = await getAlertStatisticsAPI(currentDateRange);
    if (res.success && res.data) {
      // 输出响应数据类型和平均持续时间的值
      console.log("Overview data type:", typeof res.data);
      console.log(
        "Avg duration type:",
        typeof res.data.avg_duration,
        "Value:",
        res.data.avg_duration
      );

      // 确保 avg_duration 是数字
      if (res.data.avg_duration) {
        res.data.avg_duration = Number(res.data.avg_duration);
      }

      overview.value = res.data;
    } else {
      ElMessage.error(res.msg || "获取总体告警统计失败");
    }
  } catch (error) {
    console.error("获取总体告警统计失败", error);
    ElMessage.error("获取总体告警统计失败");
  } finally {
    overviewLoading.value = false;
  }
};

const loadInstanceData = async () => {
  if (!currentDateRange.start_time || !currentDateRange.end_time) {
    ElMessage.warning("请选择日期范围");
    return;
  }

  instanceLoading.value = true;
  try {
    const params = {
      ...currentDateRange,
      page: instanceCurrentPage.value,
      limit: instancePageSize.value,
      sort_column: instanceSortColumn.value,
      sort_order: instanceSortOrder.value
    };

    const res = await getAlertStatisticsByInstanceAPI(params);
    if (res.success) {
      if (res.data && res.data.length > 0) {
        instanceTableData.value = res.data;
        instanceTableTotal.value = res.count || res.data.length;
      } else {
        // 无数据处理，不显示错误
        instanceTableData.value = [];
        instanceTableTotal.value = 0;
      }
    } else if (res.msg && res.msg !== "无数据") {
      // 只有在不是无数据的情况下才显示错误信息
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("获取实例告警统计失败", error);
    // 保留控制台错误，但不显示给用户
    instanceTableData.value = [];
    instanceTableTotal.value = 0;
  } finally {
    instanceLoading.value = false;
  }
};

const loadRuleData = async () => {
  if (!currentDateRange.start_time || !currentDateRange.end_time) {
    ElMessage.warning("请选择日期范围");
    return;
  }

  ruleLoading.value = true;
  try {
    const params = {
      ...currentDateRange,
      page: ruleCurrentPage.value,
      limit: rulePageSize.value,
      sort_column: ruleSortColumn.value,
      sort_order: ruleSortOrder.value
    };

    const res = await getAlertStatisticsByRuleAPI(params);
    if (res.success) {
      if (res.data && res.data.length > 0) {
        ruleTableData.value = res.data;
        ruleTableTotal.value = res.count || res.data.length;
      } else {
        // 无数据处理，不显示错误
        ruleTableData.value = [];
        ruleTableTotal.value = 0;
      }
    } else if (res.msg && res.msg !== "无数据") {
      // 只有在不是无数据的情况下才显示错误信息
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("获取规则告警统计失败", error);
    // 保留控制台错误，但不显示给用户
    ruleTableData.value = [];
    ruleTableTotal.value = 0;
  } finally {
    ruleLoading.value = false;
  }
};

const loadClusterData = async () => {
  if (!currentDateRange.start_time || !currentDateRange.end_time) {
    ElMessage.warning("请选择日期范围");
    return;
  }

  clusterLoading.value = true;
  try {
    const params = {
      ...currentDateRange,
      page: clusterCurrentPage.value,
      limit: clusterPageSize.value,
      sort_column: clusterSortColumn.value,
      sort_order: clusterSortOrder.value
    };

    const res = await getAlertStatisticsByClusterAPI(params);
    if (res.success) {
      if (res.data && res.data.length > 0) {
        clusterTableData.value = res.data;
        clusterTableTotal.value = res.count || res.data.length;
      } else {
        // 无数据处理，不显示错误
        clusterTableData.value = [];
        clusterTableTotal.value = 0;
      }
    } else if (res.msg && res.msg !== "无数据") {
      // 只有在不是无数据的情况下才显示错误信息
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("获取集群告警统计失败", error);
    // 保留控制台错误，但不显示给用户
    clusterTableData.value = [];
    clusterTableTotal.value = 0;
  } finally {
    clusterLoading.value = false;
  }
};

// 处理表格排序
const handleInstanceSortChange = (sort: { prop: string; order: string }) => {
  if (sort.prop && sort.order) {
    instanceSortColumn.value = sort.prop;
    instanceSortOrder.value = sort.order;
  } else {
    instanceSortColumn.value = "total_count";
    instanceSortOrder.value = "descending";
  }
  loadInstanceData();
};

const handleRuleSortChange = (sort: { prop: string; order: string }) => {
  if (sort.prop && sort.order) {
    ruleSortColumn.value = sort.prop;
    ruleSortOrder.value = sort.order;
  } else {
    ruleSortColumn.value = "trigger_count";
    ruleSortOrder.value = "descending";
  }
  loadRuleData();
};

const handleClusterSortChange = (sort: { prop: string; order: string }) => {
  if (sort.prop && sort.order) {
    clusterSortColumn.value = sort.prop;
    clusterSortOrder.value = sort.order;
  } else {
    clusterSortColumn.value = "total_count";
    clusterSortOrder.value = "descending";
  }
  loadClusterData();
};

// 处理分页
const handleInstanceSizeChange = (size: number) => {
  instancePageSize.value = size;
  loadInstanceData();
};

const handleInstancePageChange = (page: number) => {
  instanceCurrentPage.value = page;
  loadInstanceData();
};

const handleRuleSizeChange = (size: number) => {
  rulePageSize.value = size;
  loadRuleData();
};

const handleRulePageChange = (page: number) => {
  ruleCurrentPage.value = page;
  loadRuleData();
};

const handleClusterSizeChange = (size: number) => {
  clusterPageSize.value = size;
  loadClusterData();
};

const handleClusterPageChange = (page: number) => {
  clusterCurrentPage.value = page;
  loadClusterData();
};

// 处理导出
const handleInstanceExport = () => {
  ElMessage.success("实例告警统计导出功能待实现");
};

const handleRuleExport = () => {
  ElMessage.success("规则告警统计导出功能待实现");
};

const handleClusterExport = () => {
  ElMessage.success("集群告警统计导出功能待实现");
};

// 辅助函数

// 日期时间格式化为 YYYY-MM-DD HH:MM:SS
const formatDateTime = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 页面加载时执行
onMounted(() => {
  // 默认加载当前日期的数据
  const now = new Date();
  selectedDay.value = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")}`;
  handleDayChange(selectedDay.value);
  // 加载所有数据
  loadAllData();
});
</script>

<style scoped>


/* 响应式设计 */
@media (width <= 768px) {
  .n9e-statistics-container {
    padding: 0.5rem;
  }
}

.n9e-statistics-container {
  min-height: 100vh;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 全局动画 */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 卡片悬停效果 */
:deep(.el-card) {
  border: 1px solid rgb(229 231 235 / 80%);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
}

:deep(.el-card:hover) {
  box-shadow: 0 8px 25px rgb(0 0 0 / 15%);
  transform: translateY(-2px);
}

/* 按钮动画 */
:deep(.el-button) {
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
}
</style>
